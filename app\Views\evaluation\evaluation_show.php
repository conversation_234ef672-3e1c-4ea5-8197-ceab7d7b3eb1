<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= esc($title) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('evaluation') ?>">Evaluation</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($activity['activity_code'] ?? 'Activity Details') ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('evaluation') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Evaluation
            </a>
            <a href="<?= base_url('evaluation/' . $activity['id'] . '/rate') ?>" class="btn btn-success">
                <i class="fas fa-star"></i> Evaluate and Rate
            </a>
        </div>
    </div>

    <!-- Activity Overview Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-clipboard-list"></i> Activity Overview</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">Activity Code</th>
                            <td><span class="badge bg-primary fs-6"><?= esc($activity['activity_code'] ?? 'N/A') ?></span></td>
                        </tr>
                        <tr>
                            <th>Activity Title</th>
                            <td><strong><?= esc($activity['title']) ?></strong></td>
                        </tr>
                        <tr>
                            <th>Activity Type</th>
                            <td>
                                <span class="badge bg-<?= $activity['activity_type'] === 'training' ? 'primary' : ($activity['activity_type'] === 'inputs' ? 'success' : ($activity['activity_type'] === 'infrastructure' ? 'warning' : 'info')) ?> fs-6">
                                    <?= ucfirst(esc($activity['activity_type'])) ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Workplan</th>
                            <td>
                                <strong><?= esc($workplan['title'] ?? 'N/A') ?></strong>
                                <?php if (!empty($workplan['start_date'])): ?>
                                    <br><small class="text-muted">
                                        <?= date('Y', strtotime($workplan['start_date'])) ?>
                                        <?php if (!empty($workplan['end_date']) && date('Y', strtotime($workplan['start_date'])) != date('Y', strtotime($workplan['end_date']))): ?>
                                            - <?= date('Y', strtotime($workplan['end_date'])) ?>
                                        <?php endif; ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Branch</th>
                            <td><?= esc($branch['name'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <th>Supervisor</th>
                            <td><?= esc($activity['supervisor_name'] ?? 'Not assigned') ?></td>
                        </tr>
                    </table>
                </div>

                <!-- Quarterly Targets & Summary -->
                <div class="col-md-6">
                    <h6><i class="fas fa-chart-bar"></i> Quarterly Targets</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-primary">Quarter 1</h6>
                                    <p class="card-text fs-5 fw-bold"><?= $activity['q_one_target'] ? number_format($activity['q_one_target'], 2) : 'N/A' ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-success">Quarter 2</h6>
                                    <p class="card-text fs-5 fw-bold"><?= $activity['q_two_target'] ? number_format($activity['q_two_target'], 2) : 'N/A' ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-warning">Quarter 3</h6>
                                    <p class="card-text fs-5 fw-bold"><?= $activity['q_three_target'] ? number_format($activity['q_three_target'], 2) : 'N/A' ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-info">Quarter 4</h6>
                                    <p class="card-text fs-5 fw-bold"><?= $activity['q_four_target'] ? number_format($activity['q_four_target'], 2) : 'N/A' ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comprehensive Summary Stats -->
                    <div class="mt-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6><i class="fas fa-chart-pie"></i> Activity Summary</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Activity Budget:</strong> <?= $activity['total_budget'] ? CURRENCY_SYMBOL . ' ' . number_format($activity['total_budget'], 2) : 'N/A' ?></p>
                                        <p><strong>Activity Status:</strong>
                                            <span class="badge bg-<?= $activity['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                <?= ucfirst(esc($activity['status'] ?? 'N/A')) ?>
                                            </span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Total Proposal Cost:</strong> <?= CURRENCY_SYMBOL ?> <?= number_format($proposalSummary['total_cost'], 2) ?></p>
                                        <p><strong>Created:</strong> <?= date('M d, Y', strtotime($activity['created_at'])) ?></p>
                                    </div>
                                </div>

                                <hr>

                                <h6><i class="fas fa-tasks"></i> All Proposals Summary</h6>
                                <div class="row text-center">
                                    <div class="col-md-2">
                                        <div class="card border-primary">
                                            <div class="card-body p-2">
                                                <h5 class="text-primary mb-1"><?= $proposalSummary['total'] ?></h5>
                                                <small>Total</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card border-success">
                                            <div class="card-body p-2">
                                                <h5 class="text-success mb-1"><?= $proposalSummary['approved'] ?></h5>
                                                <small>Approved</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card border-warning">
                                            <div class="card-body p-2">
                                                <h5 class="text-warning mb-1"><?= $proposalSummary['pending'] ?></h5>
                                                <small>Pending</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card border-danger">
                                            <div class="card-body p-2">
                                                <h5 class="text-danger mb-1"><?= $proposalSummary['rejected'] ?></h5>
                                                <small>Rejected</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card border-info">
                                            <div class="card-body p-2">
                                                <h5 class="text-info mb-1"><?= $proposalSummary['rated'] ?></h5>
                                                <small>Rated</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card border-dark">
                                            <div class="card-body p-2">
                                                <h5 class="text-dark mb-1"><?= count($proposals) ?></h5>
                                                <small>For Evaluation</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <?php if (!empty($activity['description'])): ?>
            <div class="mt-4">
                <h6><i class="fas fa-file-text"></i> Description</h6>
                <div class="p-3 bg-light rounded">
                    <?= nl2br(esc($activity['description'])) ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Approved or Rated Proposals - Detailed Implementation -->
    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-clipboard-check"></i> Approved or Rated Proposals - Detailed Implementation</h5>
            <small>Approved proposals or proposals with ratings are shown below with full implementation details</small>
        </div>
        <div class="card-body">
            <?php if (empty($proposals)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> No approved or rated proposals found for this activity.
                    Proposals must be approved or have ratings to appear in the evaluation system.
                </div>
            <?php else: ?>
                <?php foreach ($proposals as $index => $proposal): ?>
                    <div class="proposal-section mb-5">
                        <!-- Proposal Header -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="text-primary">
                                <i class="fas fa-lightbulb"></i> Proposal #<?= $index + 1 ?>
                                <span class="badge bg-<?= $proposal['status'] === 'approved' ? 'success' : ($proposal['status'] === 'pending' ? 'warning' : 'secondary') ?>">
                                    <?= ucfirst(esc($proposal['status'] ?? 'N/A')) ?>
                                </span>
                            </h5>
                            <small class="text-muted">Created: <?= date('M d, Y', strtotime($proposal['created_at'])) ?></small>
                        </div>

                        <!-- Proposal Details -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> Proposal Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <th style="width: 40%">Duration</th>
                                                <td>
                                                    <?= $proposal['date_start'] ? date('M d, Y', strtotime($proposal['date_start'])) : 'N/A' ?>
                                                    <?php if ($proposal['date_end']): ?>
                                                        - <?= date('M d, Y', strtotime($proposal['date_end'])) ?>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Location</th>
                                                <td><?= esc($proposal['location'] ?? 'N/A') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Province</th>
                                                <td><?= esc($proposal['province_name'] ?? 'N/A') ?></td>
                                            </tr>
                                            <tr>
                                                <th>District</th>
                                                <td><?= esc($proposal['district_name'] ?? 'N/A') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Total Cost</th>
                                                <td><strong><?= $proposal['total_cost'] ? number_format($proposal['total_cost'], 2) : 'N/A' ?></strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-users"></i> Personnel & Rating</h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <th style="width: 40%">Supervisor</th>
                                                <td><?= esc($proposal['supervisor_name'] ?? 'Not assigned') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Action Officer</th>
                                                <td><?= esc($proposal['action_officer_name'] ?? 'Not assigned') ?></td>
                                            </tr>
                                            <?php if (!empty($proposal['rating_score'])): ?>
                                            <tr>
                                                <th>Rating</th>
                                                <td>
                                                    <span class="badge bg-warning fs-6"><?= esc($proposal['rating_score']) ?>/10</span>
                                                    <?php if ($proposal['rated_at']): ?>
                                                        <br><small class="text-muted">Rated: <?= date('M d, Y', strtotime($proposal['rated_at'])) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endif; ?>
                                            <?php if (!empty($proposal['rate_remarks'])): ?>
                                            <tr>
                                                <th>Rating Remarks</th>
                                                <td><small><?= esc($proposal['rate_remarks']) ?></small></td>
                                            </tr>
                                            <?php endif; ?>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Implementation Activities -->
                        <?php if (!empty($proposal['implementations'])): ?>
                            <div class="implementations-section">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-cogs"></i> Implementation Activities
                                    <span class="badge bg-success"><?= count($proposal['implementations']) ?></span>
                                </h6>

                                <?php foreach ($proposal['implementations'] as $impl_index => $implementation): ?>
                                    <div class="card mb-3 border-success">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">
                                                <span class="badge bg-<?= $implementation['type'] === 'Training' ? 'primary' : ($implementation['type'] === 'Input' ? 'success' : ($implementation['type'] === 'Infrastructure' ? 'warning' : 'info')) ?>">
                                                    <?= esc($implementation['type']) ?>
                                                </span>
                                                Implementation #<?= $impl_index + 1 ?>
                                                <small class="text-muted float-end">
                                                    <?= date('M d, Y', strtotime($implementation['created_at'])) ?>
                                                </small>
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <!-- Training Implementation Details -->
                                                    <?php if ($implementation['type'] === 'Training'): ?>
                                                        <h6><i class="fas fa-graduation-cap"></i> Training Details</h6>

                                                        <?php if (!empty($implementation['trainers'])): ?>
                                                            <div class="mb-3">
                                                                <strong><i class="fas fa-chalkboard-teacher"></i> Trainers:</strong>
                                                                <div class="bg-light p-2 rounded">
                                                                    <?= nl2br(esc($implementation['trainers'])) ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($implementation['topics'])): ?>
                                                            <div class="mb-3">
                                                                <strong><i class="fas fa-list"></i> Topics Covered:</strong>
                                                                <div class="bg-light p-2 rounded">
                                                                    <?= nl2br(esc($implementation['topics'])) ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($implementation['trainees'])): ?>
                                                            <div class="mb-3">
                                                                <strong><i class="fas fa-users"></i> Trainees List:</strong>
                                                                <div class="bg-light p-2 rounded">
                                                                    <?php
                                                                    $trainees = is_string($implementation['trainees']) ? json_decode($implementation['trainees'], true) : $implementation['trainees'];
                                                                    if (is_array($trainees)) {
                                                                        echo '<div class="row">';
                                                                        foreach ($trainees as $index => $trainee) {
                                                                            echo '<div class="col-md-6 mb-2">';
                                                                            echo '<div class="card card-body p-2">';
                                                                            echo '<small><strong>' . ($index + 1) . '.</strong> ';
                                                                            if (is_array($trainee)) {
                                                                                echo esc($trainee['name'] ?? $trainee['full_name'] ?? 'N/A');
                                                                                if (!empty($trainee['organization'])) {
                                                                                    echo '<br><em>' . esc($trainee['organization']) . '</em>';
                                                                                }
                                                                                if (!empty($trainee['position'])) {
                                                                                    echo '<br><small>' . esc($trainee['position']) . '</small>';
                                                                                }
                                                                            } else {
                                                                                echo esc($trainee);
                                                                            }
                                                                            echo '</small></div></div>';
                                                                        }
                                                                        echo '</div>';
                                                                    } else {
                                                                        echo esc($implementation['trainees']);
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <?php if ($implementation['type'] === 'Input'): ?>
                                                        <h6><i class="fas fa-box"></i> Input Implementation Details</h6>

                                                        <?php if (!empty($implementation['inputs'])): ?>
                                                            <div class="mb-3">
                                                                <strong><i class="fas fa-list-ul"></i> Input Items:</strong>
                                                                <div class="bg-light p-2 rounded">
                                                                    <?php
                                                                    $inputs = is_string($implementation['inputs']) ? json_decode($implementation['inputs'], true) : $implementation['inputs'];
                                                                    if (is_array($inputs)) {
                                                                        echo '<div class="table-responsive">';
                                                                        echo '<table class="table table-sm table-bordered">';
                                                                        echo '<thead><tr><th>#</th><th>Item</th><th>Quantity</th><th>Unit</th><th>Description</th></tr></thead>';
                                                                        echo '<tbody>';
                                                                        foreach ($inputs as $index => $input) {
                                                                            echo '<tr>';
                                                                            echo '<td>' . ($index + 1) . '</td>';
                                                                            echo '<td><strong>' . esc($input['item'] ?? $input['name'] ?? 'N/A') . '</strong></td>';
                                                                            echo '<td>' . esc($input['quantity'] ?? 'N/A') . '</td>';
                                                                            echo '<td>' . esc($input['unit'] ?? 'N/A') . '</td>';
                                                                            echo '<td>' . esc($input['description'] ?? 'N/A') . '</td>';
                                                                            echo '</tr>';
                                                                        }
                                                                        echo '</tbody></table>';
                                                                        echo '</div>';
                                                                    } else {
                                                                        echo '<div class="p-2">' . nl2br(esc($implementation['inputs'])) . '</div>';
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <?php if ($implementation['type'] === 'Infrastructure'): ?>
                                                        <h6><i class="fas fa-building"></i> Infrastructure Implementation Details</h6>

                                                        <?php if (!empty($implementation['infrastructure'])): ?>
                                                            <div class="mb-3">
                                                                <strong><i class="fas fa-hammer"></i> Infrastructure Description:</strong>
                                                                <div class="bg-light p-2 rounded">
                                                                    <?= nl2br(esc($implementation['infrastructure'])) ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <?php if ($implementation['type'] === 'Output'): ?>
                                                        <h6><i class="fas fa-chart-line"></i> Output Implementation Details</h6>

                                                        <?php if (!empty($implementation['outputs'])): ?>
                                                            <div class="mb-3">
                                                                <strong><i class="fas fa-chart-bar"></i> Output Items:</strong>
                                                                <div class="bg-light p-2 rounded">
                                                                    <?php
                                                                    $outputs = is_string($implementation['outputs']) ? json_decode($implementation['outputs'], true) : $implementation['outputs'];
                                                                    if (is_array($outputs)) {
                                                                        echo '<div class="table-responsive">';
                                                                        echo '<table class="table table-sm table-bordered">';
                                                                        echo '<thead><tr><th>#</th><th>Output Description</th><th>Quantity</th><th>Value</th><th>Unit</th></tr></thead>';
                                                                        echo '<tbody>';
                                                                        foreach ($outputs as $index => $output) {
                                                                            echo '<tr>';
                                                                            echo '<td>' . ($index + 1) . '</td>';
                                                                            echo '<td><strong>' . esc($output['description'] ?? $output['name'] ?? 'N/A') . '</strong></td>';
                                                                            echo '<td>' . esc($output['quantity'] ?? 'N/A') . '</td>';
                                                                            echo '<td>' . (isset($output['value']) ? number_format($output['value'], 2) : 'N/A') . '</td>';
                                                                            echo '<td>' . esc($output['unit'] ?? 'N/A') . '</td>';
                                                                            echo '</tr>';
                                                                        }
                                                                        echo '</tbody></table>';
                                                                        echo '</div>';
                                                                    } else {
                                                                        echo '<div class="p-2">' . nl2br(esc($implementation['outputs'])) . '</div>';
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-4">
                                                    <!-- Additional Information -->
                                                    <div class="card bg-light">
                                                        <div class="card-body">
                                                            <h6><i class="fas fa-info"></i> Additional Information</h6>

                                                            <?php if (!empty($implementation['delivery_date'])): ?>
                                                                <p><strong><i class="fas fa-calendar"></i> Delivery Date:</strong><br>
                                                                <span class="badge bg-info"><?= date('M d, Y', strtotime($implementation['delivery_date'])) ?></span></p>
                                                            <?php endif; ?>

                                                            <?php if (!empty($implementation['delivery_location'])): ?>
                                                                <p><strong><i class="fas fa-map-marker-alt"></i> Delivery Location:</strong><br>
                                                                <?= esc($implementation['delivery_location']) ?></p>
                                                            <?php endif; ?>

                                                            <?php if (!empty($implementation['total_value'])): ?>
                                                                <p><strong><i class="fas fa-dollar-sign"></i> Total Value:</strong><br>
                                                                <span class="badge bg-success fs-6"><?= number_format($implementation['total_value'], 2) ?></span></p>
                                                            <?php endif; ?>

                                                            <?php if (!empty($implementation['gps_coordinates'])): ?>
                                                                <p><strong><i class="fas fa-map-pin"></i> GPS Coordinates:</strong><br>
                                                                <small class="font-monospace bg-white p-1 rounded"><?= esc($implementation['gps_coordinates']) ?></small></p>
                                                            <?php endif; ?>

                                                            <p><strong><i class="fas fa-clock"></i> Implementation Date:</strong><br>
                                                            <small><?= date('M d, Y H:i', strtotime($implementation['created_at'])) ?></small></p>
                                                        </div>
                                                    </div>

                                                    <!-- Beneficiaries Section -->
                                                    <?php if (!empty($implementation['beneficiaries'])): ?>
                                                    <div class="card bg-light mt-3">
                                                        <div class="card-body">
                                                            <h6><i class="fas fa-users"></i> Beneficiaries</h6>
                                                            <div class="beneficiaries-list">
                                                                <?php
                                                                $beneficiaries = is_string($implementation['beneficiaries']) ? json_decode($implementation['beneficiaries'], true) : $implementation['beneficiaries'];
                                                                if (is_array($beneficiaries)) {
                                                                    echo '<div class="row">';
                                                                    foreach ($beneficiaries as $index => $beneficiary) {
                                                                        echo '<div class="col-12 mb-2">';
                                                                        echo '<div class="card card-body p-2">';
                                                                        echo '<small><strong>' . ($index + 1) . '.</strong> ';
                                                                        if (is_array($beneficiary)) {
                                                                            echo esc($beneficiary['name'] ?? $beneficiary['full_name'] ?? 'N/A');
                                                                            if (!empty($beneficiary['organization'])) {
                                                                                echo '<br><em>' . esc($beneficiary['organization']) . '</em>';
                                                                            }
                                                                            if (!empty($beneficiary['contact'])) {
                                                                                echo '<br><small>' . esc($beneficiary['contact']) . '</small>';
                                                                            }
                                                                        } else {
                                                                            echo esc($beneficiary);
                                                                        }
                                                                        echo '</small></div></div>';
                                                                    }
                                                                    echo '</div>';
                                                                } else {
                                                                    echo '<div class="p-2">' . nl2br(esc($implementation['beneficiaries'])) . '</div>';
                                                                }
                                                                ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <?php if (!empty($implementation['remarks'])): ?>
                                                <div class="mt-3">
                                                    <h6><i class="fas fa-comment"></i> Remarks</h6>
                                                    <div class="alert alert-info">
                                                        <?= nl2br(esc($implementation['remarks'])) ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> No implementation activities recorded for this proposal yet.
                            </div>
                        <?php endif; ?>

                        <?php if ($index < count($proposals) - 1): ?>
                            <hr class="my-5">
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
