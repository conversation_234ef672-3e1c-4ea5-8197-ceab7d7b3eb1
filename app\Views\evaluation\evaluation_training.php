<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= esc($title) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('evaluation') ?>">Evaluation</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($activity['activity_code'] ?? 'Training Activity') ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('evaluation') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Evaluation
            </a>
            <a href="<?= base_url('evaluation/' . $activity['id'] . '/rate') ?>" class="btn btn-success">
                <i class="fas fa-star"></i> Evaluate and Rate
            </a>
            <button onclick="window.print()" class="btn btn-info">
                <i class="fas fa-print"></i> Print Report
            </button>
        </div>
    </div>

    <!-- Activity Overview Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-graduation-cap"></i> Training Activity Overview</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">Activity Code</th>
                            <td><span class="badge bg-primary fs-6"><?= esc($activity['activity_code'] ?? 'N/A') ?></span></td>
                        </tr>
                        <tr>
                            <th>Training Title</th>
                            <td><strong><?= esc($activity['title']) ?></strong></td>
                        </tr>
                        <tr>
                            <th>Activity Type</th>
                            <td><span class="badge bg-primary fs-6">Training</span></td>
                        </tr>
                        <tr>
                            <th>Workplan</th>
                            <td>
                                <strong><?= esc($workplan['title'] ?? 'N/A') ?></strong>
                                <?php if (!empty($workplan['start_date'])): ?>
                                    <br><small class="text-muted">
                                        <?= date('Y', strtotime($workplan['start_date'])) ?>
                                        <?php if (!empty($workplan['end_date']) && date('Y', strtotime($workplan['start_date'])) != date('Y', strtotime($workplan['end_date']))): ?>
                                            - <?= date('Y', strtotime($workplan['end_date'])) ?>
                                        <?php endif; ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Branch</th>
                            <td><?= esc($branch['name'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <th>Supervisor</th>
                            <td><?= esc($activity['supervisor_name'] ?? 'Not assigned') ?></td>
                        </tr>
                    </table>
                </div>

                <!-- Training Performance & Evaluation -->
                <div class="col-md-6">
                    <h6><i class="fas fa-chart-line"></i> Training Performance & Evaluation</h6>

                    <!-- Quarterly Performance Table -->
                    <div class="table-responsive mb-3">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Quarter</th>
                                    <th>Target</th>
                                    <th>Achieved</th>
                                    <th>%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-primary">Q1</span></td>
                                    <td><?= $activity['q_one_target'] ? number_format($activity['q_one_target'], 0) : 'N/A' ?></td>
                                    <td><?= $activity['q_one_achieved'] ? number_format($activity['q_one_achieved'], 0) : 'N/A' ?></td>
                                    <td>
                                        <?php
                                        $q1_percent = ($activity['q_one_target'] && $activity['q_one_achieved'])
                                            ? round(($activity['q_one_achieved'] / $activity['q_one_target']) * 100, 1)
                                            : 0;
                                        ?>
                                        <span class="badge <?= $q1_percent >= 90 ? 'bg-success' : ($q1_percent >= 70 ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= $q1_percent ?>%
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">Q2</span></td>
                                    <td><?= $activity['q_two_target'] ? number_format($activity['q_two_target'], 0) : 'N/A' ?></td>
                                    <td><?= $activity['q_two_achieved'] ? number_format($activity['q_two_achieved'], 0) : 'N/A' ?></td>
                                    <td>
                                        <?php
                                        $q2_percent = ($activity['q_two_target'] && $activity['q_two_achieved'])
                                            ? round(($activity['q_two_achieved'] / $activity['q_two_target']) * 100, 1)
                                            : 0;
                                        ?>
                                        <span class="badge <?= $q2_percent >= 90 ? 'bg-success' : ($q2_percent >= 70 ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= $q2_percent ?>%
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">Q3</span></td>
                                    <td><?= $activity['q_three_target'] ? number_format($activity['q_three_target'], 0) : 'N/A' ?></td>
                                    <td><?= $activity['q_three_achieved'] ? number_format($activity['q_three_achieved'], 0) : 'N/A' ?></td>
                                    <td>
                                        <?php
                                        $q3_percent = ($activity['q_three_target'] && $activity['q_three_achieved'])
                                            ? round(($activity['q_three_achieved'] / $activity['q_three_target']) * 100, 1)
                                            : 0;
                                        ?>
                                        <span class="badge <?= $q3_percent >= 90 ? 'bg-success' : ($q3_percent >= 70 ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= $q3_percent ?>%
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-info">Q4</span></td>
                                    <td><?= $activity['q_four_target'] ? number_format($activity['q_four_target'], 0) : 'N/A' ?></td>
                                    <td><?= $activity['q_four_achieved'] ? number_format($activity['q_four_achieved'], 0) : 'N/A' ?></td>
                                    <td>
                                        <?php
                                        $q4_percent = ($activity['q_four_target'] && $activity['q_four_achieved'])
                                            ? round(($activity['q_four_achieved'] / $activity['q_four_target']) * 100, 1)
                                            : 0;
                                        ?>
                                        <span class="badge <?= $q4_percent >= 90 ? 'bg-success' : ($q4_percent >= 70 ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= $q4_percent ?>%
                                        </span>
                                    </td>
                                </tr>
                                <tr class="table-secondary">
                                    <td><strong>Total</strong></td>
                                    <td><strong>
                                        <?php
                                        $total_target = ($activity['q_one_target'] ?? 0) + ($activity['q_two_target'] ?? 0) +
                                                       ($activity['q_three_target'] ?? 0) + ($activity['q_four_target'] ?? 0);
                                        echo $total_target ? number_format($total_target, 0) : 'N/A';
                                        ?>
                                    </strong></td>
                                    <td><strong>
                                        <?php
                                        $total_achieved = ($activity['q_one_achieved'] ?? 0) + ($activity['q_two_achieved'] ?? 0) +
                                                         ($activity['q_three_achieved'] ?? 0) + ($activity['q_four_achieved'] ?? 0);
                                        echo $total_achieved ? number_format($total_achieved, 0) : 'N/A';
                                        ?>
                                    </strong></td>
                                    <td><strong>
                                        <?php
                                        $total_percent = ($total_target && $total_achieved)
                                            ? round(($total_achieved / $total_target) * 100, 1)
                                            : 0;
                                        ?>
                                        <span class="badge <?= $total_percent >= 90 ? 'bg-success' : ($total_percent >= 70 ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= $total_percent ?>%
                                        </span>
                                    </strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Rating Section -->
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6><i class="fas fa-star"></i> Activity Rating</h6>
                            <?php if (!empty($activity['rating']) && $activity['rating'] > 0): ?>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-3">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?= $i <= $activity['rating'] ? 'text-warning' : 'text-muted' ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="badge bg-success fs-6"><?= $activity['rating'] ?>/5</span>
                                </div>
                                <?php if (!empty($activity['rated_at'])): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> Rated on <?= date('M d, Y', strtotime($activity['rated_at'])) ?>
                                    </small>
                                <?php endif; ?>
                                <?php if (!empty($activity['reated_remarks'])): ?>
                                    <div class="mt-2">
                                        <small><strong>Evaluation Remarks:</strong></small>
                                        <div class="small text-muted"><?= nl2br(esc($activity['reated_remarks'])) ?></div>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-star-o fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">Not yet rated</p>
                                    <a href="<?= base_url('evaluation/' . $activity['id'] . '/rate') ?>" class="btn btn-sm btn-success mt-2">
                                        <i class="fas fa-star"></i> Rate Now
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                </div>
            </div>

            <!-- Training Summary Stats -->
            <div class="mt-4">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title text-primary">Training Budget</h6>
                                <p class="card-text fs-5 fw-bold">₱<?= $activity['total_budget'] ? number_format($activity['total_budget'], 2) : '0.00' ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h6 class="card-title text-info">Total Proposals</h6>
                                <p class="card-text fs-5 fw-bold"><?= $proposalSummary['total'] ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h6 class="card-title text-success">Approved</h6>
                                <p class="card-text fs-5 fw-bold"><?= $proposalSummary['approved'] ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h6 class="card-title text-warning">For Evaluation</h6>
                                <p class="card-text fs-5 fw-bold"><?= count($proposals) ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <?php if (!empty($activity['description'])): ?>
            <div class="mt-4">
                <h6><i class="fas fa-file-text"></i> Training Description</h6>
                <div class="p-3 bg-light rounded">
                    <?= nl2br(esc($activity['description'])) ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Training Proposals and Implementation Details -->
    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-chalkboard-teacher"></i> Training Proposals - Implementation Details</h5>
            <small>Approved or rated training proposals with full implementation details</small>
        </div>
        <div class="card-body">
            <?php if (empty($proposals)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> No approved or rated training proposals found for this activity.
                </div>
            <?php else: ?>
                <?php foreach ($proposals as $index => $proposal): ?>
                    <div class="proposal-section mb-5">
                        <!-- Proposal Header -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="text-primary">
                                <i class="fas fa-graduation-cap"></i> Training Proposal #<?= $index + 1 ?>
                                <span class="badge bg-<?= $proposal['status'] === 'approved' ? 'success' : ($proposal['status'] === 'pending' ? 'warning' : 'secondary') ?>">
                                    <?= ucfirst(esc($proposal['status'] ?? 'N/A')) ?>
                                </span>
                            </h5>
                            <small class="text-muted">Created: <?= date('M d, Y', strtotime($proposal['created_at'])) ?></small>
                        </div>

                        <!-- Proposal Details -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> Training Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <th style="width: 40%">Training Period</th>
                                                <td>
                                                    <?= $proposal['date_start'] ? date('M d, Y', strtotime($proposal['date_start'])) : 'N/A' ?>
                                                    <?php if ($proposal['date_end']): ?>
                                                        - <?= date('M d, Y', strtotime($proposal['date_end'])) ?>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Training Venue</th>
                                                <td><?= esc($proposal['location'] ?? 'N/A') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Province</th>
                                                <td><?= esc($proposal['province_name'] ?? 'N/A') ?></td>
                                            </tr>
                                            <tr>
                                                <th>District</th>
                                                <td><?= esc($proposal['district_name'] ?? 'N/A') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Training Cost</th>
                                                <td><strong><?= $proposal['total_cost'] ? number_format($proposal['total_cost'], 2) : 'N/A' ?></strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-users"></i> Personnel & Rating</h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <th style="width: 40%">Supervisor</th>
                                                <td><?= esc($proposal['supervisor_name'] ?? 'Not assigned') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Action Officer</th>
                                                <td><?= esc($proposal['action_officer_name'] ?? 'Not assigned') ?></td>
                                            </tr>
                                            <?php if (!empty($proposal['rating_score'])): ?>
                                            <tr>
                                                <th>Rating</th>
                                                <td>
                                                    <span class="badge bg-warning fs-6"><?= esc($proposal['rating_score']) ?>/10</span>
                                                    <?php if ($proposal['rated_at']): ?>
                                                        <br><small class="text-muted">Rated: <?= date('M d, Y', strtotime($proposal['rated_at'])) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endif; ?>
                                            <?php if (!empty($proposal['rate_remarks'])): ?>
                                            <tr>
                                                <th>Rating Remarks</th>
                                                <td><small><?= esc($proposal['rate_remarks']) ?></small></td>
                                            </tr>
                                            <?php endif; ?>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Training Implementation Activities -->
                        <?php if (!empty($proposal['implementations'])): ?>
                            <div class="implementations-section">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-chalkboard-teacher"></i> Training Implementation Details
                                    <span class="badge bg-success"><?= count($proposal['implementations']) ?></span>
                                </h6>

                                <?php foreach ($proposal['implementations'] as $impl_index => $implementation): ?>
                                    <?php if ($implementation['type'] === 'Training'): ?>
                                        <div class="card mb-4 border-primary">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">
                                                    <span class="badge bg-primary">Training Session</span>
                                                    Implementation #<?= $impl_index + 1 ?>
                                                    <small class="text-muted float-end">
                                                        <?= date('M d, Y', strtotime($implementation['created_at'])) ?>
                                                    </small>
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <!-- Training Details -->
                                                        <?php if (!empty($implementation['trainers'])): ?>
                                                            <div class="mb-3">
                                                                <h6><i class="fas fa-chalkboard-teacher"></i> Trainers</h6>
                                                                <div class="bg-light p-3 rounded">
                                                                    <?= nl2br(esc($implementation['trainers'])) ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($implementation['topics'])): ?>
                                                            <div class="mb-3">
                                                                <h6><i class="fas fa-list"></i> Topics Covered</h6>
                                                                <div class="bg-light p-3 rounded">
                                                                    <?= nl2br(esc($implementation['topics'])) ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>

                                                        <!-- Trainees List -->
                                                        <?php if (!empty($implementation['trainees'])): ?>
                                                            <div class="mb-3">
                                                                <h6><i class="fas fa-users"></i> Trainees</h6>
                                                                <div class="bg-light p-3 rounded">
                                                                    <?php
                                                                    // Handle trainees data - could be JSON string or already parsed array
                                                                    $trainees = $implementation['trainees'];
                                                                    if (is_string($trainees)) {
                                                                        $trainees = json_decode($trainees, true);
                                                                    }

                                                                    if (is_array($trainees) && !empty($trainees)) {
                                                                        echo '<div class="table-responsive">';
                                                                        echo '<table class="table table-sm table-bordered">';
                                                                        echo '<thead class="table-primary">';
                                                                        echo '<tr>';
                                                                        echo '<th>#</th>';
                                                                        echo '<th>Full Name</th>';
                                                                        echo '<th>Age</th>';
                                                                        echo '<th>Gender</th>';
                                                                        echo '<th>Phone</th>';
                                                                        echo '<th>Email</th>';
                                                                        echo '<th>Remarks</th>';
                                                                        echo '</tr>';
                                                                        echo '</thead>';
                                                                        echo '<tbody>';
                                                                        foreach ($trainees as $index => $trainee) {
                                                                            echo '<tr>';
                                                                            echo '<td>' . ($index + 1) . '</td>';
                                                                            if (is_array($trainee)) {
                                                                                echo '<td><strong>' . esc($trainee['full_name'] ?? $trainee['name'] ?? 'N/A') . '</strong></td>';
                                                                                echo '<td>' . esc($trainee['age'] ?? 'N/A') . '</td>';
                                                                                echo '<td>' . esc($trainee['gender'] ?? 'N/A') . '</td>';
                                                                                echo '<td>' . esc($trainee['phone'] ?? $trainee['contact'] ?? 'N/A') . '</td>';
                                                                                echo '<td>' . esc($trainee['email'] ?? 'N/A') . '</td>';
                                                                                echo '<td>' . esc($trainee['remarks'] ?? $trainee['notes'] ?? 'N/A') . '</td>';
                                                                            } else {
                                                                                echo '<td colspan="6">' . esc($trainee) . '</td>';
                                                                            }
                                                                            echo '</tr>';
                                                                        }
                                                                        echo '</tbody></table>';
                                                                        echo '</div>';
                                                                        echo '<p class="mt-2"><strong>Total Trainees: </strong><span class="badge bg-primary">' . count($trainees) . '</span></p>';
                                                                    } else {
                                                                        echo '<p>' . esc($implementation['trainees']) . '</p>';
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="col-md-4">
                                                        <!-- Training Information -->
                                                        <div class="card bg-light">
                                                            <div class="card-body">
                                                                <h6><i class="fas fa-info-circle"></i> Training Info</h6>

                                                                <?php if (!empty($implementation['gps_coordinates'])): ?>
                                                                    <p><strong><i class="fas fa-map-pin"></i> GPS Location:</strong><br>
                                                                    <small class="font-monospace bg-white p-1 rounded"><?= esc($implementation['gps_coordinates']) ?></small></p>
                                                                <?php endif; ?>

                                                                <p><strong><i class="fas fa-clock"></i> Implementation Date:</strong><br>
                                                                <small><?= date('M d, Y H:i', strtotime($implementation['created_at'])) ?></small></p>

                                                                <?php if (!empty($implementation['signing_sheet_filepath'])): ?>
                                                                    <p><strong><i class="fas fa-file-signature"></i> Signing Sheet:</strong><br>
                                                                    <a href="<?= base_url($implementation['signing_sheet_filepath']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                        <i class="fas fa-download"></i> Download
                                                                    </a></p>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                        <!-- Training Images -->
                                                        <?php if (!empty($implementation['training_images'])): ?>
                                                            <div class="card bg-light mt-3">
                                                                <div class="card-body">
                                                                    <h6><i class="fas fa-images"></i> Training Photos</h6>
                                                                    <?php
                                                                    $images = is_string($implementation['training_images']) ? json_decode($implementation['training_images'], true) : $implementation['training_images'];
                                                                    if (is_array($images) && !empty($images)) {
                                                                        echo '<div class="row">';
                                                                        foreach ($images as $image) {
                                                                            $imagePath = is_array($image) ? ($image['path'] ?? $image['url'] ?? '') : $image;
                                                                            if (!empty($imagePath)) {
                                                                                echo '<div class="col-6 mb-2">';
                                                                                echo '<img src="' . base_url($imagePath) . '" class="img-fluid rounded" style="max-height: 100px; cursor: pointer;" onclick="window.open(this.src)" title="Click to view full size">';
                                                                                echo '</div>';
                                                                            }
                                                                        }
                                                                        echo '</div>';
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>

                                                        <!-- Training Files -->
                                                        <?php if (!empty($implementation['training_files'])): ?>
                                                            <div class="card bg-light mt-3">
                                                                <div class="card-body">
                                                                    <h6><i class="fas fa-file-alt"></i> Training Files</h6>
                                                                    <?php
                                                                    $files = is_string($implementation['training_files']) ? json_decode($implementation['training_files'], true) : $implementation['training_files'];
                                                                    if (is_array($files) && !empty($files)) {
                                                                        foreach ($files as $file) {
                                                                            $filePath = is_array($file) ? ($file['path'] ?? $file['url'] ?? '') : $file;
                                                                            $fileName = is_array($file) ? ($file['name'] ?? basename($filePath)) : basename($filePath);
                                                                            if (!empty($filePath)) {
                                                                                echo '<div class="mb-2">';
                                                                                echo '<a href="' . base_url($filePath) . '" target="_blank" class="btn btn-sm btn-outline-secondary">';
                                                                                echo '<i class="fas fa-download"></i> ' . esc($fileName);
                                                                                echo '</a></div>';
                                                                            }
                                                                        }
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <?php if (!empty($implementation['remarks'])): ?>
                                                    <div class="mt-3">
                                                        <h6><i class="fas fa-comment"></i> Training Remarks</h6>
                                                        <div class="alert alert-info">
                                                            <?= nl2br(esc($implementation['remarks'])) ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> No training implementation activities recorded for this proposal yet.
                            </div>
                        <?php endif; ?>

                        <?php if ($index < count($proposals) - 1): ?>
                            <hr class="my-5">
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
