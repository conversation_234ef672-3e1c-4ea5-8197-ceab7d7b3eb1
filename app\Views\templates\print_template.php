<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'AMIS Evaluation Report' ?></title>

    <!-- Bootstrap CSS for basic styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
            margin: 0;
            padding: 20px;
        }

        /* Print-specific styles */
        @media print {
            body {
                margin: 0;
                padding: 15px;
                font-size: 12px;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-print {
                display: none !important;
            }
        }

        /* Professional layout styles */
        .print-container {
            max-width: 100%;
            margin: 0 auto;
            background-color: #fff;
        }

        .evaluation-header {
            text-align: center;
            border-bottom: 3px solid #0d6efd;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .evaluation-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: #0d6efd;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .evaluation-header h2 {
            font-size: 1.5rem;
            font-weight: 500;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .evaluation-header p {
            color: #6c757d;
            margin-bottom: 5px;
            font-size: 1rem;
        }

        .info-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #0d6efd;
            border-bottom: 2px solid #0d6efd;
            padding-bottom: 8px;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .info-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            padding: 12px;
            border: 1px solid #dee2e6;
            text-align: left;
            width: 30%;
        }

        .info-table td {
            padding: 12px;
            border: 1px solid #dee2e6;
            vertical-align: top;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        .metric-card h4 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0d6efd;
            margin-bottom: 10px;
        }

        .metric-card p {
            margin: 0;
            color: #6c757d;
            font-weight: 500;
        }

        .proposal-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 30px;
            overflow: hidden;
            page-break-inside: avoid;
        }

        .proposal-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .proposal-header h4 {
            margin: 0;
            color: #0d6efd;
            font-weight: 600;
        }

        .implementation-item {
            border-left: 4px solid #0d6efd;
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            page-break-inside: avoid;
        }

        .implementation-item h6 {
            color: #0d6efd;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .content-box {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 15px;
        }

        .content-box h6 {
            color: #0d6efd;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .trainees-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            margin-top: 10px;
        }

        .trainees-table th,
        .trainees-table td {
            border: 1px solid #dee2e6;
            padding: 6px;
            text-align: left;
        }

        .trainees-table th {
            background-color: #e3f2fd;
            font-weight: 600;
        }

        .section-divider {
            border-top: 2px solid #dee2e6;
            margin: 40px 0;
        }

        /* Print button styles */
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .print-controls .btn {
            margin-left: 10px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .performance-grid {
                grid-template-columns: 1fr;
            }
            
            .print-container {
                padding: 10px;
            }
        }
    </style>

    <?= $this->renderSection('styles') ?>
</head>
<body>
    <!-- Print Controls (hidden when printing) -->
    <div class="print-controls no-print">
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print"></i> Print Report
        </button>
        <button onclick="window.close()" class="btn btn-secondary">
            <i class="fas fa-times"></i> Close
        </button>
    </div>

    <!-- Main Content Container -->
    <div class="print-container">
        <?= $this->renderSection('content') ?>
    </div>

    <!-- Bootstrap JS (minimal for print functionality) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus for print dialog
        window.addEventListener('load', function() {
            // Optional: Auto-open print dialog when page loads
            // window.print();
        });
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>

    <?= $this->renderSection('scripts') ?>
</body>
</html>
